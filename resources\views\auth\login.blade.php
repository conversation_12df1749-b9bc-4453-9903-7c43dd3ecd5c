@extends('layouts.auth')

@section('title', 'Connexion')

@section('content')
<div class="logo-container text-center">
    <img src="{{ asset('assets/images/logo_gradis.png') }}" alt="GRADIS Logo" class="mb-4 mx-auto" style="max-width: 150px;">
</div>

<div class="welcome-section text-center mb-8">
    <h1 class="welcome-title mb-2">Bienvenue à</h1>
    <h2 class="company-name mb-1">GRANDEUR DISTRIBUTION & SERVICES</h2>
    <p class="company-subtitle">(GRADIS SARL U)</p>
</div>

@if (session('status'))
    <div class="alert alert-success mb-4">
        {{ session('status') }}
    </div>
@endif

@if (session('error'))
    <div class="alert alert-danger mb-4">
        {{ session('error') }}
    </div>
@endif

<form method="POST" action="{{ route('login') }}" class="space-y-6">
    @csrf

    <div class="form-group">
        <label for="email" class="form-label">Adresse email</label>
        <input id="email" type="email" name="email" value="{{ old('email') }}" 
               class="form-control @error('email') is-invalid @enderror" 
               required autofocus>
        @error('email')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="form-group">
        <label for="password" class="form-label">Mot de passe</label>
        <div class="position-relative">
            <input id="password" type="password" name="password"
                   class="form-control @error('password') is-invalid @enderror"
                   required>
            <button type="button" class="btn btn-link position-absolute top-50 end-0 translate-middle-y pe-3"
                    id="togglePassword" style="border: none; background: none; z-index: 10;">
                <i class="fas fa-eye" id="eyeIcon"></i>
            </button>
        </div>
        @error('password')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="form-check">
            <input type="checkbox" name="remember" id="remember" class="form-check-input">
            <label class="form-check-label" for="remember">Se souvenir de moi</label>
        </div>

        @if (Route::has('password.request'))
            <a href="{{ route('password.request') }}" class="text-primary">
                Mot de passe oublié ?
            </a>
        @endif
    </div>

    <button type="submit" class="btn btn-primary w-100">
        Se connecter
    </button>
</form>
@endsection
