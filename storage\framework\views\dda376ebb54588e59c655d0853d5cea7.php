<?php $__env->startSection('title', 'Bons de Commande - Gestionnaire Ciment'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        --border-radius: 15px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .page-header {
        background: var(--primary-gradient);
        border-radius: var(--border-radius);
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .page-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .page-header p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 0;
        position: relative;
        z-index: 2;
    }

    .stats-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: var(--primary-gradient);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--card-hover-shadow);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }

    .stats-icon.primary { background: var(--primary-gradient); }
    .stats-icon.success { background: var(--success-gradient); }
    .stats-icon.warning { background: var(--warning-gradient); }
    .stats-icon.info { background: var(--info-gradient); }

    .supply-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        border: none;
        overflow: hidden;
        margin-bottom: 1.5rem;
    }

    .supply-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--card-hover-shadow);
    }

    .supply-card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
        position: relative;
    }

    .supply-card-body {
        padding: 1.5rem;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-validated {
        background: linear-gradient(135deg, #11998e, #38ef7d);
        color: white;
    }

    .status-pending {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;
    }

    .product-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-left: 4px solid #667eea;
    }

    .btn-modern {
        border-radius: 25px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: var(--transition);
        border: none;
    }

    .btn-primary-modern {
        background: var(--primary-gradient);
        color: white;
    }

    .btn-primary-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-info-modern {
        background: var(--info-gradient);
        color: white;
    }

    .btn-info-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        color: white;
    }

    .search-box {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .search-input {
        border: 2px solid #e9ecef;
        border-radius: 25px;
        padding: 0.75rem 1.5rem;
        transition: var(--transition);
    }

    .search-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
    }

    .empty-state-icon {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }

    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }

        .stats-card {
            margin-bottom: 1rem;
        }

        .supply-card-header,
        .supply-card-body {
            padding: 1rem;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid fade-in">
    <!-- En-tête de la page -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1><i class="fas fa-truck-loading me-3"></i>Bons de Commande</h1>
                <p>Gérez vos approvisionnements validés et suivez les livraisons en temps réel</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex justify-content-md-end align-items-center">
                    <button class="btn btn-light btn-modern me-2" onclick="refreshData()">
                        <i class="fas fa-sync-alt me-2"></i>Actualiser
                    </button>
                    <div class="text-white-50">
                        <small>Dernière mise à jour: <span id="lastUpdate"><?php echo e(now()->format('H:i')); ?></span></small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-boxes"></i>
                </div>
                <h3 class="mb-1"><?php echo e($supplies->count()); ?></h3>
                <p class="text-muted mb-0">Approvisionnements Validés</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card">
                <div class="stats-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="mb-1"><?php echo e($supplies->where('status', 'validated')->count()); ?></h3>
                <p class="text-muted mb-0">Prêts pour Livraison</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card">
                <div class="stats-icon info">
                    <i class="fas fa-truck"></i>
                </div>
                <h3 class="mb-1"><?php echo e($supplies->sum(function($supply) { return $supply->cities->count(); })); ?></h3>
                <p class="text-muted mb-0">Destinations Totales</p>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="stats-card">
                <div class="stats-icon warning">
                    <i class="fas fa-weight-hanging"></i>
                </div>
                <h3 class="mb-1"><?php echo e(number_format($supplies->sum(function($supply) { return $supply->details->sum('quantity'); }), 0)); ?></h3>
                <p class="text-muted mb-0">Tonnage Total</p>
            </div>
        </div>
    </div>

    <!-- Barre de recherche et filtres -->
    <div class="search-box">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text bg-transparent border-end-0">
                        <i class="fas fa-search text-muted"></i>
                    </span>
                    <input type="text" class="form-control search-input border-start-0"
                           placeholder="Rechercher par fournisseur, produit ou référence..."
                           id="searchInput">
                </div>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary active" data-filter="all">
                        <i class="fas fa-list me-1"></i>Tous
                    </button>
                    <button type="button" class="btn btn-outline-success" data-filter="validated">
                        <i class="fas fa-check me-1"></i>Validés
                    </button>
                    <button type="button" class="btn btn-outline-info" data-filter="recent">
                        <i class="fas fa-clock me-1"></i>Récents
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des approvisionnements -->
    <div class="row" id="suppliesContainer">
        <?php $__empty_1 = true; $__currentLoopData = $supplies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="col-12 supply-item" data-search="<?php echo e(strtolower($supply->supplier->name ?? '')); ?> <?php echo e(strtolower($supply->reference ?? '')); ?>">
                <div class="supply-card">
                    <div class="supply-card-header">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-industry text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <h5 class="mb-1 fw-bold"><?php echo e($supply->supplier->name ?? 'Fournisseur non défini'); ?></h5>
                                        <p class="text-muted mb-0">
                                            <i class="fas fa-calendar-alt me-2"></i>
                                            <?php echo e($supply->created_at->format('d/m/Y à H:i')); ?>

                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <span class="status-badge status-<?php echo e($supply->status); ?>">
                                    <i class="fas fa-check-circle me-1"></i><?php echo e(ucfirst($supply->status)); ?>

                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="supply-card-body">
                        <!-- Informations de validation -->
                        <?php if($supply->validator): ?>
                            <div class="alert alert-success border-0 mb-3" style="background: linear-gradient(135deg, rgba(17, 153, 142, 0.1), rgba(56, 239, 125, 0.1));">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user-check text-success me-2"></i>
                                    <span><strong>Validé par:</strong> <?php echo e($supply->validator->first_name); ?> <?php echo e($supply->validator->last_name); ?></span>
                                    <span class="ms-auto text-muted">
                                        <i class="fas fa-clock me-1"></i><?php echo e($supply->updated_at->diffForHumans()); ?>

                                    </span>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Détails des produits -->
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <h6 class="fw-bold mb-3">
                                    <i class="fas fa-boxes me-2 text-primary"></i>Produits commandés
                                </h6>
                                <?php $__currentLoopData = $supply->details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="product-item">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-1 fw-bold"><?php echo e($detail->product->name ?? 'Produit non défini'); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo e($detail->product->category->name ?? 'Catégorie non définie'); ?>

                                                </small>
                                            </div>
                                            <div class="col-md-3 text-center">
                                                <span class="badge bg-primary rounded-pill">
                                                    <?php echo e(number_format($detail->quantity, 0)); ?> T
                                                </span>
                                            </div>
                                            <div class="col-md-3 text-end">
                                                <?php if($detail->product->prices->isNotEmpty()): ?>
                                                    <strong class="text-success">
                                                        <?php echo e(number_format($detail->product->prices->first()->price, 0)); ?> FCFA/T
                                                    </strong>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>

                            <div class="col-md-4">
                                <h6 class="fw-bold mb-3">
                                    <i class="fas fa-map-marker-alt me-2 text-danger"></i>Destinations
                                </h6>
                                <div class="destinations-list">
                                    <?php $__currentLoopData = $supply->cities->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $citySupply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-2"
                                                 style="width: 30px; height: 30px;">
                                                <i class="fas fa-city text-muted" style="font-size: 0.8rem;"></i>
                                            </div>
                                            <span><?php echo e($citySupply->city->name ?? 'Ville non définie'); ?></span>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($supply->cities->count() > 3): ?>
                                        <small class="text-muted">
                                            <i class="fas fa-plus me-1"></i><?php echo e($supply->cities->count() - 3); ?> autres destinations
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                            <div class="d-flex align-items-center">
                                <span class="text-muted me-3">
                                    <i class="fas fa-user me-1"></i>
                                    Créé par: <?php echo e($supply->createdBy->first_name ?? 'Utilisateur'); ?> <?php echo e($supply->createdBy->last_name ?? ''); ?>

                                </span>
                            </div>
                            <div>
                                <a href="<?php echo e(route('cement-manager.supplies.show', $supply->id)); ?>"
                                   class="btn btn-info-modern btn-modern">
                                    <i class="fas fa-eye me-2"></i>Voir Détails
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-12">
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <h4 class="text-muted mb-3">Aucun approvisionnement trouvé</h4>
                    <p class="text-muted mb-4">Il n'y a actuellement aucun approvisionnement validé disponible.</p>
                    <a href="<?php echo e(route('cement-manager.dashboard')); ?>" class="btn btn-primary-modern btn-modern">
                        <i class="fas fa-arrow-left me-2"></i>Retour au tableau de bord
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Éléments DOM
    const searchInput = document.getElementById('searchInput');
    const filterButtons = document.querySelectorAll('[data-filter]');
    const suppliesContainer = document.getElementById('suppliesContainer');
    const supplyItems = document.querySelectorAll('.supply-item');
    const lastUpdateSpan = document.getElementById('lastUpdate');

    // Fonction de recherche
    function performSearch() {
        const searchTerm = searchInput.value.toLowerCase().trim();

        supplyItems.forEach(item => {
            const searchData = item.getAttribute('data-search');
            const isVisible = searchData.includes(searchTerm);

            if (isVisible) {
                item.style.display = 'block';
                item.classList.add('fade-in');
            } else {
                item.style.display = 'none';
                item.classList.remove('fade-in');
            }
        });

        // Afficher message si aucun résultat
        const visibleItems = Array.from(supplyItems).filter(item => item.style.display !== 'none');
        updateEmptyState(visibleItems.length === 0 && searchTerm !== '');
    }

    // Fonction de filtrage
    function performFilter(filterType) {
        const currentDate = new Date();
        const threeDaysAgo = new Date(currentDate.getTime() - (3 * 24 * 60 * 60 * 1000));

        supplyItems.forEach(item => {
            let isVisible = true;

            switch(filterType) {
                case 'validated':
                    const statusBadge = item.querySelector('.status-validated');
                    isVisible = statusBadge !== null;
                    break;
                case 'recent':
                    // Logique pour les éléments récents (à adapter selon vos besoins)
                    isVisible = true; // Placeholder
                    break;
                case 'all':
                default:
                    isVisible = true;
                    break;
            }

            if (isVisible) {
                item.style.display = 'block';
                item.classList.add('fade-in');
            } else {
                item.style.display = 'none';
                item.classList.remove('fade-in');
            }
        });

        // Mettre à jour les boutons de filtre
        filterButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-filter') === filterType) {
                btn.classList.add('active');
            }
        });
    }

    // Fonction pour afficher/masquer l'état vide
    function updateEmptyState(show) {
        let emptyState = document.querySelector('.empty-state-search');

        if (show && !emptyState) {
            emptyState = document.createElement('div');
            emptyState.className = 'col-12 empty-state-search';
            emptyState.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h4 class="text-muted mb-3">Aucun résultat trouvé</h4>
                    <p class="text-muted mb-4">Essayez de modifier vos critères de recherche.</p>
                    <button class="btn btn-primary-modern btn-modern" onclick="clearSearch()">
                        <i class="fas fa-times me-2"></i>Effacer la recherche
                    </button>
                </div>
            `;
            suppliesContainer.appendChild(emptyState);
        } else if (!show && emptyState) {
            emptyState.remove();
        }
    }

    // Fonction pour effacer la recherche
    window.clearSearch = function() {
        searchInput.value = '';
        performSearch();
        searchInput.focus();
    };

    // Fonction pour actualiser les données
    window.refreshData = function() {
        const refreshBtn = document.querySelector('[onclick="refreshData()"]');
        const originalContent = refreshBtn.innerHTML;

        refreshBtn.innerHTML = '<span class="loading-spinner me-2"></span>Actualisation...';
        refreshBtn.disabled = true;

        // Simuler le rechargement (remplacer par un appel AJAX si nécessaire)
        setTimeout(() => {
            location.reload();
        }, 1000);
    };

    // Écouteurs d'événements
    searchInput.addEventListener('input', performSearch);
    searchInput.addEventListener('keyup', function(e) {
        if (e.key === 'Escape') {
            clearSearch();
        }
    });

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filterType = this.getAttribute('data-filter');
            performFilter(filterType);
        });
    });

    // Animation au survol des cartes
    document.querySelectorAll('.supply-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Mise à jour de l'heure
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });
        if (lastUpdateSpan) {
            lastUpdateSpan.textContent = timeString;
        }
    }

    // Mettre à jour l'heure toutes les minutes
    setInterval(updateTime, 60000);

    // Animation d'entrée pour les éléments
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, { threshold: 0.1 });

    document.querySelectorAll('.supply-card, .stats-card').forEach(el => {
        observer.observe(el);
    });

    // Raccourcis clavier
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K pour focus sur la recherche
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            searchInput.focus();
        }

        // Ctrl/Cmd + R pour actualiser
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            refreshData();
        }
    });

    // Tooltip pour les raccourcis
    searchInput.setAttribute('title', 'Raccourci: Ctrl+K');

    console.log('Interface Cement Manager initialisée avec succès! 🚀');
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.cement_manager', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/cement-manager/supplies/index.blade.php ENDPATH**/ ?>