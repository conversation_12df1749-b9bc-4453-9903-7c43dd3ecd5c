<?php

namespace App\Http\Controllers\Accountant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\Payment;

class RecoveryController extends Controller
{
    /**
     * Display a listing of the sales with their payment status.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // Préparer la requête de base
        $query = Sale::with(['payments']);
        
        // Appliquer les filtres si présents
        // Gérer le filtre par statut (paramètre 'status' de la vue)
        if ($request->has('status') && !empty($request->status)) {
            $query->where('payment_status', $request->status);
        }
        // Maintenir la compatibilité avec l'ancien paramètre 'payment_status'
        elseif ($request->has('payment_status') && !empty($request->payment_status)) {
            $query->where('payment_status', $request->payment_status);
        }
        
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_phone', 'like', "%{$search}%");
            });
        }
        
        if ($request->has('date_range') && !empty($request->date_range)) {
            $range = $request->date_range;
            if ($range === 'today') {
                $query->whereDate('created_at', now()->toDateString());
            } elseif ($range === 'week') {
                $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
            } elseif ($range === 'month') {
                $query->whereBetween('created_at', [now()->startOfMonth(), now()->endOfMonth()]);
            } elseif ($range === 'year') {
                $query->whereBetween('created_at', [now()->startOfYear(), now()->endOfYear()]);
            }
        }
        
        // Récupérer les ventes filtrées
        $sales = $query->orderBy('created_at', 'desc')
                     ->paginate(10)
                     ->withQueryString();

        // Date actuelle pour le calcul de l'ancienneté
        $now = now();
        
        // Statistiques pour le tableau de bord
        $totalSales = Sale::count();
        $paidSales = Sale::where('payment_status', 'paid')->count();
        $partialSales = Sale::where('payment_status', 'partial')->count();
        $unpaidSales = Sale::where('payment_status', 'unpaid')->count();
        
        // Calculer le statut de paiement pour chaque vente
        foreach ($sales as $sale) {
            $totalAmount = $sale->total_amount;
            $paidAmount = $sale->amount_paid;
            
            // Débogage pour vérifier les valeurs des numéros de facture
            if (empty($sale->invoice_number)) {
                // Si le numéro de facture est vide, générer un numéro temporaire basé sur l'ID
                $sale->invoice_number = 'VENTE-' . str_pad($sale->id, 6, '0', STR_PAD_LEFT);
            }
            
            // Utiliser le statut de paiement existant ou le calculer si nécessaire
            // IMPORTANT: Ne pas recalculer le statut pour les ventes annulées par l'admin
            if (!in_array($sale->payment_status, ['completed', 'partial', 'pending', 'cancelled'])) {
                $sale->payment_status = $this->calculatePaymentStatus($totalAmount, $paidAmount);
            }
            
            // Calculer l'ancienneté de la vente en jours
            $saleDate = $sale->created_at;
            $daysSinceCreation = $saleDate->diffInDays($now);
            $sale->days_since_creation = $daysSinceCreation;
            
            // Déterminer la classe CSS pour la coloration en fonction de l'ancienneté et du statut de paiement
            if ($sale->payment_status !== 'paid') {
                if ($daysSinceCreation >= 45) {
                    $sale->row_class = 'overdue-critical';
                } elseif ($daysSinceCreation >= 30) {
                    $sale->row_class = 'overdue-warning';
                } elseif ($daysSinceCreation >= 15) {
                    $sale->row_class = 'overdue-notice';
                } else {
                    $sale->row_class = '';
                }
            } else {
                $sale->row_class = '';
            }
            
            // Mapper les statuts du modèle vers ceux de notre interface
            // IMPORTANT: Préserver le statut 'cancelled' pour les ventes annulées par l'admin
            if ($sale->payment_status === 'cancelled') {
                // Ne pas modifier le statut 'cancelled' pour préserver la logique d'annulation admin
                $sale->payment_status = 'cancelled';
            } elseif ($sale->payment_status === 'completed') {
                $sale->payment_status = 'paid';
            } elseif ($sale->payment_status === 'partial') {
                $sale->payment_status = 'partial';
            } else {
                $sale->payment_status = 'unpaid';
            }
            
            $sale->paid_amount = $paidAmount;
            // Utiliser l'accesseur existant pour remaining_amount
            $sale->payment_percentage = $sale->getPaymentProgressAttribute();
        }

        return view('accountant.recoveries.index', compact(
            'sales',
            'totalSales',
            'paidSales',
            'partialSales',
            'unpaidSales'
        ));
    }

    /**
     * Calculate the payment status based on the total and paid amounts.
     *
     * @param float $totalAmount
     * @param float $paidAmount
     * @return string
     */
    private function calculatePaymentStatus($totalAmount, $paidAmount)
    {
        if ($paidAmount >= $totalAmount) {
            return 'paid';
        } elseif ($paidAmount > 0) {
            return 'partial';
        } else {
            return 'unpaid';
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // Cette méthode sera implémentée plus tard
        return redirect()->route('accountant.recoveries.index');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Cette méthode sera implémentée plus tard
        return redirect()->route('accountant.recoveries.index');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $sale = Sale::with(['payments'])->findOrFail($id);
        
        // Calculer les statistiques de paiement
        $totalAmount = $sale->total_amount;
        $paidAmount = $sale->amount_paid;
        $remainingAmount = $totalAmount - $paidAmount;
        $paymentPercentage = ($totalAmount > 0) ? ($paidAmount / $totalAmount) * 100 : 0;
        
        // Déterminer le statut de paiement
        if ($paidAmount >= $totalAmount) {
            $paymentStatus = 'paid';
        } elseif ($paidAmount > 0) {
            $paymentStatus = 'partial';
        } else {
            $paymentStatus = 'unpaid';
        }
        
        return view('accountant.recoveries.show', compact(
            'sale', 
            'totalAmount', 
            'paidAmount', 
            'remainingAmount', 
            'paymentPercentage',
            'paymentStatus'
        ));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $sale = Sale::with(['payments'])->findOrFail($id);
        
        // Calculer les statistiques de paiement
        $totalAmount = $sale->total_amount;
        $paidAmount = $sale->amount_paid;
        $remainingAmount = $totalAmount - $paidAmount;
        $paymentPercentage = ($totalAmount > 0) ? ($paidAmount / $totalAmount) * 100 : 0;
        
        // Déterminer le statut de paiement
        if ($paidAmount >= $totalAmount) {
            $paymentStatus = 'paid';
        } elseif ($paidAmount > 0) {
            $paymentStatus = 'partial';
        } else {
            $paymentStatus = 'unpaid';
        }
        
        // Récupérer l'historique des paiements
        $payments = $sale->payments()->orderBy('created_at', 'desc')->get();
        
        return view('accountant.recoveries.edit', compact(
            'sale', 
            'totalAmount', 
            'paidAmount', 
            'remainingAmount', 
            'paymentPercentage',
            'paymentStatus',
            'payments'
        ));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Valider les données du formulaire
        $validated = $request->validate([
            'amount' => 'required|numeric|min:1',
            'payment_method' => 'required|string',
            'reference_number' => 'nullable|string|max:255',
            'payment_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
        ]);
        
        // Récupérer la vente
        $sale = Sale::findOrFail($id);
        
        // Vérifier que le montant du paiement ne dépasse pas le montant restant dû
        $remainingAmount = $sale->total_amount - $sale->amount_paid;
        if ($validated['amount'] > $remainingAmount) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['amount' => 'Le montant du paiement ne peut pas dépasser le montant restant dû.']);
        }
        
        // Déterminer le poste de l'utilisateur (pour la référence)
        $position = auth()->user()->position ?? 'Comptable';
        // Utiliser le nom complet du poste sans espaces ni caractères spéciaux
        $positionCode = strtoupper(preg_replace('/[^a-zA-Z0-9]/', '', $position));
        
        // Obtenir le dernier rang de paiement
        $lastPayment = Payment::latest('id')->first();
        $lastId = $lastPayment ? $lastPayment->id + 1 : 1;
        $paymentRank = str_pad($lastId, 6, '0', STR_PAD_LEFT);
        
        // Générer la référence unique du paiement
        $reference = 'RGMT' . $positionCode . $paymentRank;
        
        // Créer un nouveau paiement
        $payment = new Payment([
            'reference' => $reference, // Référence unique générée automatiquement
            'sale_id' => $sale->id,
            'cashier_id' => auth()->id(), // ID de l'utilisateur connecté (comptable)
            'position' => $position, // Poste de l'utilisateur
            'amount' => $validated['amount'],
            'payment_method' => $validated['payment_method'],
            'reference_number' => $validated['reference_number'],
            'payment_date' => $validated['payment_date'],
            'notes' => $validated['notes'] ?? ('Paiement de ' . number_format($validated['amount'], 0, ',', ' ') . ' FCFA par ' . auth()->user()->name . ' (' . $position . ')'),
            'status' => 'completed',
        ]);
        
        // Enregistrer le paiement
        $payment->save();
        
        // Mettre à jour le montant payé et le statut de la vente
        $sale->amount_paid += $validated['amount'];
        
        // Déterminer le nouveau statut de paiement
        if ($sale->amount_paid >= $sale->total_amount) {
            $sale->payment_status = 'paid';
        } elseif ($sale->amount_paid > 0) {
            $sale->payment_status = 'partial';
        } else {
            $sale->payment_status = 'unpaid';
        }
        
        // Sauvegarder les modifications
        $sale->save();
        
        return redirect()->route('accountant.recoveries.show', $sale->id)
            ->with('success', 'Paiement enregistré avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // Cette méthode sera implémentée plus tard
        return redirect()->route('accountant.recoveries.index');
    }
}
