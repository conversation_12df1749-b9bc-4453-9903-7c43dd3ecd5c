<?php $__env->startSection('title', 'Liste des Véhicules'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card mt-5">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title m-0">Liste des Véhicules</h3>
                    <div class="card-tools">
                        <a href="<?php echo e(route('cement-manager.trucks.create')); ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Ajouter un véhicule
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>N° d'Immatriculation</th>
                                    <th>Marque</th>
                                    <th><PERSON><PERSON><PERSON><PERSON></th>
                                    <th>Année</th>
                                    <th>Capacité</th>
                                    <th>Statut</th>
                                    <th>Notes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $trucks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $truck): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($truck->registration_number); ?></td>
                                    <td><?php echo e($truck->brand); ?></td>
                                    <td><?php echo e($truck->model); ?></td>
                                    <td><?php echo e($truck->year); ?></td>
                                    <td><?php echo e($truck->capacity ? number_format($truck->capacity->tonnage, 2) . ' T' : 'N/A'); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo e($truck->status === 'available' ? 'success' : ($truck->status === 'maintenance' ? 'warning' : 'danger')); ?>">
                                            <?php echo e($truck->status === 'available' ? 'Disponible' : ($truck->status === 'maintenance' ? 'En maintenance' : 'Occupé')); ?>

                                        </span>
                                    </td>
                                    <td><?php echo e($truck->notes); ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="<?php echo e(route('cement-manager.trucks.show', $truck->id)); ?>" class="btn btn-sm btn-info" title="Voir les détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .table th, .table td {
        vertical-align: middle;
    }
    .btn-group {
        gap: 5px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.cement-manager', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/cement-manager/trucks/index.blade.php ENDPATH**/ ?>