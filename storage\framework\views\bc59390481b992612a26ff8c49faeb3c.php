<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title><?php echo e(config('app.name', 'GRADIS')); ?> - <?php echo $__env->yieldContent('title'); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom styles -->
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .auth-wrapper {
            width: 100%;
            max-width: 400px;
            margin: auto;
        }

        .auth-card {
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }

        .form-control {
            border-radius: 8px;
            padding: 0.75rem 1rem;
            border: 1px solid #e2e8f0;
        }

        .form-control:focus {
            border-color: #4f46e5;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
        }

        .btn-primary {
            background-color: #4f46e5;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background-color: #4338ca;
            transform: translateY(-1px);
        }

        .text-primary {
            color: #4f46e5 !important;
            text-decoration: none;
        }

        .text-primary:hover {
            color: #4338ca !important;
            text-decoration: underline;
        }

        .alert {
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #dcfce7;
            border: 1px solid #86efac;
            color: #166534;
        }

        .alert-danger {
            background-color: #fee2e2;
            border: 1px solid #fca5a5;
            color: #991b1b;
        }

        .invalid-feedback {
            color: #dc2626;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .form-check-input:checked {
            background-color: #4f46e5;
            border-color: #4f46e5;
        }

        /* Styles pour le bouton toggle password */
        #togglePassword {
            color: #6b7280;
            padding: 0;
            margin: 0;
            width: auto;
            height: auto;
            transition: color 0.2s ease;
        }

        #togglePassword:hover {
            color: #4f46e5;
        }

        #togglePassword:focus {
            box-shadow: none;
            outline: none;
        }

        .position-relative input[type="password"],
        .position-relative input[type="text"] {
            padding-right: 3rem;
        }

        /* Styles pour la section de bienvenue */
        .welcome-section {
            margin-bottom: 2rem;
        }

        .welcome-title {
            font-size: 1.5rem;
            font-weight: 400;
            color: #6b7280;
            margin-bottom: 0.5rem;
            letter-spacing: 0.5px;
        }

        .company-name {
            font-size: 1.75rem;
            font-weight: 700;
            color: #4f46e5;
            line-height: 1.2;
            margin-bottom: 0.25rem;
            text-shadow: 0 1px 2px rgba(79, 70, 229, 0.1);
        }

        .company-subtitle {
            font-size: 1rem;
            font-weight: 500;
            color: #9ca3af;
            margin: 0;
            font-style: italic;
        }

        /* Animation d'apparition */
        .welcome-section {
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design pour les petits écrans */
        @media (max-width: 480px) {
            .welcome-title {
                font-size: 1.25rem;
            }

            .company-name {
                font-size: 1.5rem;
                line-height: 1.3;
            }

            .company-subtitle {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="auth-wrapper">
        <div class="auth-card">
            <?php echo $__env->yieldContent('content'); ?>
            <footer class="footer mt-auto py-3">
                <div class="container text-center">
                    <span class="text-muted">© <?php echo e(date('Y')); ?> GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
                </div>
            </footer>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Script pour le toggle du mot de passe -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const togglePassword = document.getElementById('togglePassword');
            const passwordField = document.getElementById('password');
            const eyeIcon = document.getElementById('eyeIcon');

            if (togglePassword && passwordField && eyeIcon) {
                togglePassword.addEventListener('click', function() {
                    // Toggle le type du champ
                    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordField.setAttribute('type', type);

                    // Toggle l'icône
                    if (type === 'password') {
                        eyeIcon.classList.remove('fa-eye-slash');
                        eyeIcon.classList.add('fa-eye');
                    } else {
                        eyeIcon.classList.remove('fa-eye');
                        eyeIcon.classList.add('fa-eye-slash');
                    }
                });
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\gradis\resources\views/layouts/auth.blade.php ENDPATH**/ ?>