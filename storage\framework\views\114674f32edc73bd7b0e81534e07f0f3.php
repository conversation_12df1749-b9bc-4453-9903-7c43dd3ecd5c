<?php $__env->startSection('title', 'Connexion'); ?>

<?php $__env->startSection('content'); ?>
<div class="logo-container text-center">
    <img src="<?php echo e(asset('assets/images/logo_gradis.png')); ?>" alt="GRADIS Logo" class="mb-4 mx-auto" style="max-width: 150px;">
</div>

<div class="welcome-section text-center mb-8">
    <h1 class="welcome-title mb-2">Bienvenue à</h1>
    <h2 class="company-name mb-1">GRANDEUR DISTRIBUTION & SERVICES</h2>
    <p class="company-subtitle">(GRADIS SARL U)</p>
</div>

<?php if(session('status')): ?>
    <div class="alert alert-success mb-4">
        <?php echo e(session('status')); ?>

    </div>
<?php endif; ?>

<?php if(session('error')): ?>
    <div class="alert alert-danger mb-4">
        <?php echo e(session('error')); ?>

    </div>
<?php endif; ?>

<form method="POST" action="<?php echo e(route('login')); ?>" class="space-y-6">
    <?php echo csrf_field(); ?>

    <div class="form-group">
        <label for="email" class="form-label">Adresse email</label>
        <input id="email" type="email" name="email" value="<?php echo e(old('email')); ?>" 
               class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
               required autofocus>
        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="invalid-feedback"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="form-group">
        <label for="password" class="form-label">Mot de passe</label>
        <div class="position-relative">
            <input id="password" type="password" name="password"
                   class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                   required>
            <button type="button" class="btn btn-link position-absolute top-50 end-0 translate-middle-y pe-3"
                    id="togglePassword" style="border: none; background: none; z-index: 10;">
                <i class="fas fa-eye" id="eyeIcon"></i>
            </button>
        </div>
        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <div class="invalid-feedback"><?php echo e($message); ?></div>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </div>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="form-check">
            <input type="checkbox" name="remember" id="remember" class="form-check-input">
            <label class="form-check-label" for="remember">Se souvenir de moi</label>
        </div>

        <?php if(Route::has('password.request')): ?>
            <a href="<?php echo e(route('password.request')); ?>" class="text-primary">
                Mot de passe oublié ?
            </a>
        <?php endif; ?>
    </div>

    <button type="submit" class="btn btn-primary w-100">
        Se connecter
    </button>
</form>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/auth/login.blade.php ENDPATH**/ ?>