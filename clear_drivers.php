<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Driver;
use Illuminate\Support\Facades\DB;

// Initialiser Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== SCRIPT DE VIDAGE DE LA TABLE DRIVERS ===\n\n";

try {
    // Compter les chauffeurs
    $driversCount = Driver::withTrashed()->count();
    
    if ($driversCount === 0) {
        echo "✅ La table drivers est déjà vide.\n";
        exit(0);
    }

    echo "Nombre total de chauffeurs trouvés : {$driversCount}\n";
    echo "⚠️  ATTENTION : Cette action va supprimer DÉFINITIVEMENT tous les chauffeurs !\n";
    echo "Tapez 'OUI' pour confirmer : ";
    
    $handle = fopen("php://stdin", "r");
    $confirmation = trim(fgets($handle));
    fclose($handle);
    
    if (strtoupper($confirmation) !== 'OUI') {
        echo "❌ Opération annulée.\n";
        exit(0);
    }

    echo "\n🔄 Début du processus de nettoyage...\n";

    DB::beginTransaction();

    // Étape 1: Libérer tous les camions
    echo "1. Libération des camions assignés...\n";
    $trucksUpdated = DB::table('trucks')->whereNotNull('driver_id')->update(['driver_id' => null]);
    echo "   → {$trucksUpdated} camions libérés\n";

    // Étape 2: Supprimer tous les chauffeurs
    echo "2. Suppression des chauffeurs...\n";
    Driver::withTrashed()->forceDelete();
    echo "   → Tous les chauffeurs supprimés\n";

    // Étape 3: Reset auto-increment
    echo "3. Reset de l'auto-increment...\n";
    DB::statement('ALTER TABLE drivers AUTO_INCREMENT = 1');
    echo "   → Auto-increment remis à 1\n";

    DB::commit();

    echo "\n✅ SUCCÈS ! La table drivers a été vidée complètement.\n";
    echo "📊 Résumé :\n";
    echo "   - {$driversCount} chauffeurs supprimés définitivement\n";
    echo "   - {$trucksUpdated} camions libérés\n";
    echo "   - Auto-increment remis à 1\n";

} catch (Exception $e) {
    DB::rollBack();
    echo "\n❌ ERREUR : " . $e->getMessage() . "\n";
    exit(1);
}
