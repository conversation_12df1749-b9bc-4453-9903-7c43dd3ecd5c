<?php

namespace App\Console\Commands;

use App\Models\Driver;
use App\Models\Truck;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ClearDriversTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fleet:clear {--drivers-only : Vider seulement la table drivers} {--trucks-only : Vider seulement la table trucks} {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vider complètement les tables drivers et trucks (suppression définitive)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $driversOnly = $this->option('drivers-only');
        $trucksOnly = $this->option('trucks-only');

        // Par défaut, vider les deux tables si aucune option spécifique
        $clearDrivers = !$trucksOnly;
        $clearTrucks = !$driversOnly;

        $driversCount = $clearDrivers ? Driver::withTrashed()->count() : 0;
        $trucksCount = $clearTrucks ? Truck::withTrashed()->count() : 0;

        if ($driversCount === 0 && $trucksCount === 0) {
            $this->info('Les tables sélectionnées sont déjà vides.');
            return 0;
        }

        $this->info('=== VIDAGE DE LA FLOTTE ===');
        if ($clearDrivers) $this->info("Chauffeurs trouvés : {$driversCount}");
        if ($clearTrucks) $this->info("Camions trouvés : {$trucksCount}");

        if (!$this->option('force')) {
            $message = 'Êtes-vous sûr de vouloir vider ';
            if ($clearDrivers && $clearTrucks) {
                $message .= 'les tables drivers ET trucks';
            } elseif ($clearDrivers) {
                $message .= 'la table drivers';
            } else {
                $message .= 'la table trucks';
            }
            $message .= ' ? Cette action est irréversible.';

            if (!$this->confirm($message)) {
                $this->info('Opération annulée.');
                return 0;
            }
        }

        try {
            DB::beginTransaction();

            if ($clearDrivers && $clearTrucks) {
                $this->clearBothTables($driversCount, $trucksCount);
            } elseif ($clearDrivers) {
                $this->clearDriversOnly($driversCount);
            } else {
                $this->clearTrucksOnly($trucksCount);
            }

            DB::commit();
            $this->info('✅ Opération terminée avec succès !');

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('❌ Erreur lors de la suppression : ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    private function clearBothTables($driversCount, $trucksCount)
    {
        $this->info('🔄 Vidage simultané des tables drivers et trucks...');

        // Étape 1: Supprimer définitivement tous les chauffeurs et camions
        $this->info('1. Suppression des chauffeurs et camions...');
        Driver::withTrashed()->forceDelete();
        Truck::withTrashed()->forceDelete();

        // Étape 2: Reset des auto-increments
        $this->info('2. Reset des auto-increments...');
        DB::statement('ALTER TABLE drivers AUTO_INCREMENT = 1');
        DB::statement('ALTER TABLE trucks AUTO_INCREMENT = 1');

        $this->info("✅ {$driversCount} chauffeurs et {$trucksCount} camions supprimés définitivement");
        $this->info('✅ Auto-increments remis à 1');
    }

    private function clearDriversOnly($driversCount)
    {
        $this->info('🔄 Vidage de la table drivers uniquement...');

        // Étape 1: Libérer les camions assignés
        $this->info('1. Libération des camions assignés...');
        $trucksUpdated = DB::table('trucks')->whereNotNull('driver_id')->update(['driver_id' => null]);

        // Étape 2: Supprimer tous les chauffeurs
        $this->info('2. Suppression des chauffeurs...');
        Driver::withTrashed()->forceDelete();

        // Étape 3: Reset auto-increment
        $this->info('3. Reset de l\'auto-increment...');
        DB::statement('ALTER TABLE drivers AUTO_INCREMENT = 1');

        $this->info("✅ {$driversCount} chauffeurs supprimés définitivement");
        $this->info("✅ {$trucksUpdated} camions libérés");
        $this->info('✅ Auto-increment remis à 1');
    }

    private function clearTrucksOnly($trucksCount)
    {
        $this->info('🔄 Vidage de la table trucks uniquement...');

        // Étape 1: Libérer les chauffeurs assignés
        $this->info('1. Libération des chauffeurs assignés...');
        $driversUpdated = DB::table('drivers')->whereNotNull('truck_id')->update(['truck_id' => null]);

        // Étape 2: Supprimer tous les camions
        $this->info('2. Suppression des camions...');
        Truck::withTrashed()->forceDelete();

        // Étape 3: Reset auto-increment
        $this->info('3. Reset de l\'auto-increment...');
        DB::statement('ALTER TABLE trucks AUTO_INCREMENT = 1');

        $this->info("✅ {$trucksCount} camions supprimés définitivement");
        $this->info("✅ {$driversUpdated} chauffeurs libérés");
        $this->info('✅ Auto-increment remis à 1');
    }
}
