<?php

namespace App\Console\Commands;

use App\Models\Driver;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ClearDriversTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'drivers:clear {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Vider complètement la table drivers (suppression définitive)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $driversCount = Driver::withTrashed()->count();
        
        if ($driversCount === 0) {
            $this->info('La table drivers est déjà vide.');
            return 0;
        }

        $this->info("Nombre total de chauffeurs trouvés : {$driversCount}");
        
        if (!$this->option('force')) {
            if (!$this->confirm('Êtes-vous sûr de vouloir vider complètement la table drivers ? Cette action est irréversible.')) {
                $this->info('Opération annulée.');
                return 0;
            }
        }

        try {
            DB::beginTransaction();

            // Étape 1: Libérer tous les camions assignés aux chauffeurs
            $this->info('Libération des camions assignés...');
            DB::table('trucks')->whereNotNull('driver_id')->update(['driver_id' => null]);

            // Étape 2: Supprimer définitivement tous les chauffeurs (y compris soft deleted)
            $this->info('Suppression des chauffeurs...');
            Driver::withTrashed()->forceDelete();

            // Étape 3: Reset de l'auto-increment
            $this->info('Reset de l\'auto-increment...');
            DB::statement('ALTER TABLE drivers AUTO_INCREMENT = 1');

            DB::commit();

            $this->info('✅ Table drivers vidée avec succès !');
            $this->info('- Tous les chauffeurs ont été supprimés définitivement');
            $this->info('- Les camions ont été libérés');
            $this->info('- L\'auto-increment a été remis à 1');

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('❌ Erreur lors de la suppression : ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
