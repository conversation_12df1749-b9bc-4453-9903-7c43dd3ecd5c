<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Libérer tous les camions assignés aux chauffeurs
        DB::table('trucks')->whereNotNull('driver_id')->update(['driver_id' => null]);
        
        // Vider complètement la table drivers (y compris les soft deleted)
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('drivers')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Cette migration ne peut pas être annulée car les données sont supprimées définitivement
        // Vous devrez restaurer les données depuis une sauvegarde si nécessaire
    }
};
